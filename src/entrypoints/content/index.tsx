import '~/assets/tailwind.css';
import type { ContentScriptContext } from '#imports';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import { getStoreInstances } from '@/store';
import { validateMessage, type Message, type MessageResponse } from '@/lib/schemas';
import { extractArticleContent } from '@/lib/extract/ReadAbility.ts';

// 全局UI实例管理
let uiInstance: globalThis.ShadowRootContentScriptUi<ReactDOM.Root> | null = null;

export default defineContentScript({
	matches: ['<all_urls>'],
	cssInjectionMode: 'ui',
	async main(ctx) {
		// 获取store实例（非React环境安全）
		const { appStore } = getStoreInstances();

		// 初始化应用状态
		appStore.getState().setCurrentUrl(window.location.href);
		appStore.getState().setInitialized(true);

		// 发送初始化消息
		await sendContentReadyMessage();

		// 创建消息生成器实例
		const messageGenerator = getMessage();

		try {
			// 使用 for await 循环持续处理消息
			for await (const message of messageGenerator) {
				await handleMessage(message, ctx);
			}
		} catch (error) {
			console.error('❌ Error in message processing loop:', error);
		}
	},
});

// 处理接收到的消息
async function handleMessage(message: Message, ctx: ContentScriptContext) {
	const sendResponse = (message as any)._sendResponse;

	try {
		console.log('🔄 Processing message:', message.action);

		// 处理不同类型的消息
		switch (message.action) {
			case 'toggleUI':
				await handleToggleUI(ctx);
				break;
			default:
				console.warn('⚠️ Unknown message action:', message.action);
		}

		const successResponse: MessageResponse = {
			success: true,
			timestamp: Date.now(),
		};
		sendResponse(successResponse);
	} catch (error: any) {
		console.error('❌ Error processing message:', error);
		const errorResponse: MessageResponse = {
			success: false,
			timestamp: Date.now(),
			error: error.message,
		};
		sendResponse(errorResponse);
	}
}

// 处理UI切换逻辑
async function handleToggleUI(ctx: ContentScriptContext) {
	const { appStore } = getStoreInstances();
	const appState = appStore.getState();

	const result = await extractArticleContent(document);
	console.log('extractArticleContent', result);

	// 切换UI可见性状态
	appState.setUIVisible(!appState.isUIVisible);

	// 获取更新后的状态
	const updatedState = appStore.getState();

	if (updatedState.isUIVisible) {
		// 显示UI - 如果还没有创建则创建
		console.log('👁️ Showing UI...');
		try {
			if (!uiInstance) {
				uiInstance = await createUI(ctx);
			}

			if (uiInstance) {
				uiInstance.mount();
			}
		} catch (error) {
			console.error('❌ Failed to show UI:', error);
			throw error;
		}
	} else {
		// 隐藏UI
		console.log('👁️ Hiding UI...');
		try {
			if (uiInstance) {
				uiInstance.remove();
				console.log('✅ UI hidden successfully');
			}
		} catch (error) {
			console.error('❌ Failed to hide UI:', error);
			throw error;
		}
	}

	console.log('🎯 UI toggle completed', {
		isUIVisible: updatedState.isUIVisible,
	});
}

// 向background script发送初始化消息
async function sendContentReadyMessage() {
	try {
		const initMessage: Message = {
			action: 'contentScriptReady',
			url: window.location.href,
			timestamp: Date.now(),
		};

		const validatedMessage = validateMessage(initMessage);
		if (validatedMessage) {
			const initResponse = await browser.runtime.sendMessage(validatedMessage);
			console.log('📤 Initialization message sent to background:', initResponse);
		}
	} catch (error) {
		console.warn('⚠️ Could not send initialization message:', error);
	}
}

// 异步生成器函数：持续监听来自background script的消息
async function* getMessage(): AsyncGenerator<Message, void, unknown> {
	// 创建一个Promise队列来处理消息
	const messageQueue: Array<{
		message: Message;
		resolve: (response: MessageResponse) => void;
	}> = [];

	let isListening = true;

	// 设置消息监听器
	const messageListener = (message: any, sender: any, sendResponse: (response: MessageResponse) => void) => {
		console.log('📨 Content script received message:', {
			message,
			sender,
			timestamp: new Date().toISOString(),
		});

		// 验证消息来源
		if (!sender.id || sender.id !== browser.runtime.id) {
			console.warn('⚠️ Message from unknown sender:', sender);
			const errorResponse: MessageResponse = {
				success: false,
				timestamp: Date.now(),
				error: 'Unknown sender',
			};
			sendResponse(errorResponse);
			return;
		}

		// 验证消息格式
		const validatedMessage = validateMessage(message);
		if (!validatedMessage) {
			console.warn('⚠️ Invalid message format:', message);
			const errorResponse: MessageResponse = {
				success: false,
				timestamp: Date.now(),
				error: 'Invalid message format',
			};
			sendResponse(errorResponse);
			return;
		}

		// 将验证后的消息添加到队列
		messageQueue.push({
			message: validatedMessage,
			resolve: sendResponse,
		});
	};

	// 注册消息监听器
	browser.runtime.onMessage.addListener(messageListener);

	try {
		// 持续处理消息队列
		while (isListening) {
			if (messageQueue.length > 0) {
				const { message, resolve } = messageQueue.shift()!;

				// 存储响应函数以便后续使用
				(message as any)._sendResponse = resolve;

				yield message;
			} else {
				// 等待一小段时间再检查队列
				await new Promise((resolve) => setTimeout(resolve, 10));
			}
		}
	} finally {
		// 清理：移除消息监听器
		browser.runtime.onMessage.removeListener(messageListener);
		console.log('🧹 Message listener cleaned up');
	}
}

// 创建UI
async function createUI(ctx: ContentScriptContext): Promise<globalThis.ShadowRootContentScriptUi<ReactDOM.Root>> {
	const ui = await createShadowRootUi(ctx, {
		name: 'example-ui',
		position: 'inline',
		anchor: 'body',
		onMount: (container) => {
			// Container is a body, and React warns when creating a root on the body, so create a wrapper div
			const app = document.createElement('div');
			app.className = 'fixed w-screen h-screen bg-amber-100 top-0 left-0 z-999';
			container.append(app);

			// Create a root on the UI container and render a component
			const root = ReactDOM.createRoot(app);
			root.render(<App />);
			return root;
		},
		onRemove: (root) => {
			// Unmount the root when the UI is removed
			root?.unmount();
		},
	});
	return ui;
}
