import * as React from 'react';
import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';

import { cn } from '@/lib/utils';

// Shadow DOM 兼容的 DropdownMenuContent 组件
function ShadowDropdownMenuContent({
	className,
	sideOffset = 4,
	container,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content> & {
	container?: Element | null;
}) {
	// 获取 Shadow Root 容器
	const getPortalContainer = React.useCallback(() => {
		if (container) return container;

		// 尝试找到最近的 Shadow Root 容器
		const findShadowContainer = (): Element => {
			// 查找应用的根容器
			let targetContainer: Element | null = null;

			// 方法1: 查找固定定位的应用容器
			targetContainer = document.querySelector('[class*="fixed"][class*="w-screen"][class*="h-screen"]');

			if (targetContainer) {
				return targetContainer;
			}

			// 方法2: 查找 .app 容器
			targetContainer = document.querySelector('.app');
			if (targetContainer) {
				return targetContainer;
			}

			// 方法3: 查找 Shadow Root
			const triggers = document.querySelectorAll('[data-slot="dropdown-menu-trigger"]');
			for (const trigger of triggers) {
				const root = trigger.getRootNode();
				if (root instanceof ShadowRoot) {
					const shadowContainer = root.firstElementChild;
					if (shadowContainer) return shadowContainer as Element;
				}
			}

			// 回退到 document.body
			return document.body;
		};

		return findShadowContainer();
	}, [container]);

	return (
		<DropdownMenuPrimitive.Portal container={getPortalContainer()}>
			<DropdownMenuPrimitive.Content
				data-slot="dropdown-menu-content"
				sideOffset={sideOffset}
				className={cn(
					'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-[9999] max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',
					className,
				)}
				{...props}
			/>
		</DropdownMenuPrimitive.Portal>
	);
}

// 导出 Shadow DOM 兼容的组件
export { ShadowDropdownMenuContent };
