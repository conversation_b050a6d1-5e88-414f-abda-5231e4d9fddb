import { z } from 'zod';

// 消息类型枚举
export const MessageActionSchema = z.enum([
  'toggleUI',
  'contentScriptReady',
  'updateSettings',
  'getSettings'
]);

// 基础消息schema
export const BaseMessageSchema = z.object({
  action: MessageActionSchema,
  timestamp: z.number(),
});

// 切换UI消息schema
export const ToggleUIMessageSchema = BaseMessageSchema.extend({
  action: z.literal('toggleUI'),
});

// Content Script就绪消息schema
export const ContentScriptReadyMessageSchema = BaseMessageSchema.extend({
  action: z.literal('contentScriptReady'),
  url: z.string().url(),
});

// 用户设置schema
export const FAMILYS = ['system', 'serif', 'sans-serif'];
export const THEMES = ['light', 'yellow', 'green', 'dark'];
export const UserSettingsSchema = z.object({
  theme: z.enum(THEMES).default('light'),
  fontSize: z.number().min(12).max(28).default(16),
  fontFamily: z.enum(FAMILYS).default('system'),
  lineHeight: z.number().min(1.0).max(2.0).default(1.5),
  contentWidth: z.number().min(480).max(1200).default(720),
  showImage: z.boolean().default(true),
  showVideo: z.boolean().default(true),
});

// 设置更新消息schema
export const UpdateSettingsMessageSchema = BaseMessageSchema.extend({
  action: z.literal('updateSettings'),
  settings: UserSettingsSchema,
});

// 获取设置消息schema
export const GetSettingsMessageSchema = BaseMessageSchema.extend({
  action: z.literal('getSettings'),
});

// 联合消息类型
export const MessageSchema = z.discriminatedUnion('action', [
  ToggleUIMessageSchema,
  ContentScriptReadyMessageSchema,
  UpdateSettingsMessageSchema,
  GetSettingsMessageSchema,
]);

// 消息响应schema
export const MessageResponseSchema = z.object({
  success: z.boolean(),
  timestamp: z.number(),
  error: z.string().optional(),
  data: z.any().optional(),
});

// 应用状态schema
export const AppStateSchema = z.object({
  isInitialized: z.boolean(),
  currentUrl: z.string().url().optional(),
  isUIVisible: z.boolean().default(false)
});

// 导出类型
export type MessageAction = z.infer<typeof MessageActionSchema>;
export type BaseMessage = z.infer<typeof BaseMessageSchema>;
export type ToggleUIMessage = z.infer<typeof ToggleUIMessageSchema>;
export type ContentScriptReadyMessage = z.infer<typeof ContentScriptReadyMessageSchema>;
export type UpdateSettingsMessage = z.infer<typeof UpdateSettingsMessageSchema>;
export type GetSettingsMessage = z.infer<typeof GetSettingsMessageSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type MessageResponse = z.infer<typeof MessageResponseSchema>;
export type UserSettings = z.infer<typeof UserSettingsSchema>;
export type AppState = z.infer<typeof AppStateSchema>;

// 验证函数
export const validateMessage = (data: unknown): Message | null => {
  try {
    return MessageSchema.parse(data);
  } catch (error) {
    console.error('❌ Message validation failed:', error);
    return null;
  }
};

export const validateMessageResponse = (data: unknown): MessageResponse | null => {
  try {
    return MessageResponseSchema.parse(data);
  } catch (error) {
    console.error('❌ Message response validation failed:', error);
    return null;
  }
};

export const validateUserSettings = (data: unknown): UserSettings | null => {
  try {
    return UserSettingsSchema.parse(data);
  } catch (error) {
    console.error('❌ User settings validation failed:', error);
    return null;
  }
};
