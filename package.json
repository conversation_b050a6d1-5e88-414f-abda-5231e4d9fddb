{"name": "wxt-react-starter", "description": "manifest.json description", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@mozilla/readability": "^0.6.0", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.10", "@tailwindcss/vite": "^4.1.13", "cash-dom": "^8.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "zod": "^4.1.8", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^24.3.3", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@wxt-dev/module-react": "^1.1.3", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "wxt": "^0.20.6"}}